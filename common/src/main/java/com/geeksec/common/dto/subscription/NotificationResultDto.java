package com.geeksec.common.dto.subscription;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知发送结果 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResultDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 告警ID
     */
    private String alarmId;
    
    /**
     * 通知渠道类型
     */
    private NotificationChannelDto.ChannelType channelType;
    
    /**
     * 接收者
     */
    private String recipient;
    
    /**
     * 发送状态
     */
    private SendStatus sendStatus;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 通知主题
     */
    private String subject;
    
    /**
     * 通知内容
     */
    private String content;
    
    /**
     * 创建成功结果
     */
    public static NotificationResultDto success(String subscriptionId, String alarmId, 
                                               NotificationChannelDto.ChannelType channelType, String recipient,
                                               String subject, String content) {
        return NotificationResultDto.builder()
                .subscriptionId(subscriptionId)
                .alarmId(alarmId)
                .channelType(channelType)
                .recipient(recipient)
                .sendStatus(SendStatus.SUCCESS)
                .sendTime(LocalDateTime.now())
                .retryCount(0)
                .subject(subject)
                .content(content)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static NotificationResultDto failure(String subscriptionId, String alarmId,
                                               NotificationChannelDto.ChannelType channelType, String recipient,
                                               String errorMessage, int retryCount) {
        return NotificationResultDto.builder()
                .subscriptionId(subscriptionId)
                .alarmId(alarmId)
                .channelType(channelType)
                .recipient(recipient)
                .sendStatus(SendStatus.FAILED)
                .sendTime(LocalDateTime.now())
                .errorMessage(errorMessage)
                .retryCount(retryCount)
                .build();
    }
}
