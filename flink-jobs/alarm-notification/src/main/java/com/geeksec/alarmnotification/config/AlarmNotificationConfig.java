package com.geeksec.alarmnotification.config;

// import com.geeksec.common.config.ConfigurationManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;

import java.io.Serializable;

/**
 * 告警通知配置类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Slf4j
public class AlarmNotificationConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // ==================== 基础配置 ====================
    
    /** 作业名称 */
    private String jobName = "alarm-notification-job";
    
    /** 作业并行度 */
    private int jobParallelism = 4;
    
    /** 检查点间隔（毫秒） */
    private long checkpointInterval = 60000L;
    
    /** 检查点超时时间（毫秒） */
    private long checkpointTimeout = 300000L;
    
    /** 最小暂停间隔（毫秒） */
    private long minPauseBetweenCheckpoints = 5000L;
    
    /** 最大并发检查点数 */
    private int maxConcurrentCheckpoints = 1;
    
    // ==================== Kafka 配置 ====================
    
    /** Kafka Bootstrap Servers */
    private String kafkaBootstrapServers = "localhost:9092";
    
    /** 消费者组ID */
    private String consumerGroupId = "alarm-notification-consumer";
    
    /** 输入主题（处理后的告警） */
    private String inputTopic = "processed-alarms";
    
    /** 订阅配置变更主题 */
    private String subscriptionChangesTopic = "subscription-changes";
    
    /** 起始偏移量 */
    private String startingOffsets = "latest";
    
    /** 自动提交 */
    private boolean autoCommit = true;
    
    /** 提交间隔（毫秒） */
    private long commitInterval = 5000L;
    
    // ==================== 通知配置 ====================
    
    /** 通知处理并行度 */
    private int notificationParallelism = 2;
    
    /** 通知批量大小 */
    private int notificationBatchSize = 50;
    
    /** 通知延迟时间（毫秒） */
    private long notificationLingerMs = 1000L;
    
    /** 告警服务基础URL */
    private String alarmServiceBaseUrl = "http://localhost:8080";
    
    /** 订阅配置刷新间隔（秒） */
    private int subscriptionRefreshIntervalSeconds = 300;
    
    /** 是否启用订阅配置定期刷新 */
    private boolean subscriptionPeriodicRefreshEnabled = true;
    
    // ==================== 邮件配置 ====================
    
    /** 邮件配置 */
    private EmailConfig emailConfig = new EmailConfig();
    
    // ==================== Kafka通知配置 ====================
    
    /** Kafka通知配置 */
    private KafkaConfig kafkaConfig = new KafkaConfig();
    
    // ==================== 监控配置 ====================
    
    /** 是否启用监控 */
    private boolean monitoringEnabled = true;
    
    /** 指标间隔（毫秒） */
    private long metricsInterval = 30000L;
    
    /** 是否启用性能日志 */
    private boolean performanceLogging = true;
    
    /** 是否启用详细指标 */
    private boolean detailedMetrics = false;
    
    // ==================== 并行度配置 ====================
    
    /** Kafka Source 并行度 */
    private int kafkaSourceParallelism = 4;
    
    /** 处理并行度 */
    private int processingParallelism = 8;
    
    /** 通知 Sink 并行度 */
    private int notificationSinkParallelism = 2;
    
    // ==================== 配置获取方法 ====================
    
    /**
     * 获取告警通知配置
     *
     * @return 配置参数工具
     */
    public static ParameterTool getConfig() {
        // 简化的配置加载，优先级：环境变量 > 系统属性 > 默认值
        try {
            ParameterTool envConfig = ParameterTool.fromMap(System.getenv());
            ParameterTool sysConfig = ParameterTool.fromSystemProperties();
            return sysConfig.mergeWith(envConfig);
        } catch (Exception e) {
            // 如果加载失败，返回空配置
            return ParameterTool.fromMap(new java.util.HashMap<>());
        }
    }
    
    /**
     * 创建配置对象
     */
    public static AlarmNotificationConfig create() {
        return fromParameterTool(getConfig());
    }
    
    /**
     * 从 ParameterTool 创建配置对象
     */
    public static AlarmNotificationConfig fromParameterTool(ParameterTool parameterTool) {
        AlarmNotificationConfig config = new AlarmNotificationConfig();
        
        // 基础配置
        config.setJobName(parameterTool.get("alarm.notification.job.name", config.getJobName()));
        config.setJobParallelism(parameterTool.getInt("alarm.notification.job.parallelism", config.getJobParallelism()));
        config.setCheckpointInterval(parameterTool.getLong("alarm.notification.checkpoint.interval", config.getCheckpointInterval()));
        config.setCheckpointTimeout(parameterTool.getLong("alarm.notification.checkpoint.timeout", config.getCheckpointTimeout()));
        
        // Kafka配置
        config.setKafkaBootstrapServers(parameterTool.get("kafka.bootstrap.servers", config.getKafkaBootstrapServers()));
        config.setConsumerGroupId(parameterTool.get("alarm.notification.consumer.group.id", config.getConsumerGroupId()));
        config.setInputTopic(parameterTool.get("alarm.notification.input.topic", config.getInputTopic()));
        config.setSubscriptionChangesTopic(parameterTool.get("alarm.notification.subscription.changes.topic", config.getSubscriptionChangesTopic()));
        config.setStartingOffsets(parameterTool.get("alarm.notification.starting.offsets", config.getStartingOffsets()));
        
        // 通知配置
        config.setNotificationParallelism(parameterTool.getInt("alarm.notification.parallelism", config.getNotificationParallelism()));
        config.setNotificationBatchSize(parameterTool.getInt("alarm.notification.batch.size", config.getNotificationBatchSize()));
        config.setNotificationLingerMs(parameterTool.getLong("alarm.notification.linger.ms", config.getNotificationLingerMs()));
        config.setAlarmServiceBaseUrl(parameterTool.get("alarm.notification.alarm.service.base.url", config.getAlarmServiceBaseUrl()));
        config.setSubscriptionRefreshIntervalSeconds(parameterTool.getInt("alarm.notification.subscription.refresh.interval.seconds", config.getSubscriptionRefreshIntervalSeconds()));
        config.setSubscriptionPeriodicRefreshEnabled(parameterTool.getBoolean("alarm.notification.subscription.periodic.refresh.enabled", config.isSubscriptionPeriodicRefreshEnabled()));
        
        // 邮件配置
        EmailConfig emailConfig = config.getEmailConfig();
        emailConfig.setSmtpHost(parameterTool.get("alarm.notification.email.smtp.host", emailConfig.getSmtpHost()));
        emailConfig.setSmtpPort(parameterTool.getInt("alarm.notification.email.smtp.port", emailConfig.getSmtpPort()));
        emailConfig.setUsername(parameterTool.get("alarm.notification.email.username", emailConfig.getUsername()));
        emailConfig.setPassword(parameterTool.get("alarm.notification.email.password", emailConfig.getPassword()));
        emailConfig.setFromAddress(parameterTool.get("alarm.notification.email.from.address", emailConfig.getFromAddress()));
        emailConfig.setFromName(parameterTool.get("alarm.notification.email.from.name", emailConfig.getFromName()));
        emailConfig.setTlsEnabled(parameterTool.getBoolean("alarm.notification.email.tls.enabled", emailConfig.isTlsEnabled()));
        
        // Kafka通知配置
        KafkaConfig kafkaConfig = config.getKafkaConfig();
        kafkaConfig.setBootstrapServers(parameterTool.get("alarm.notification.kafka.bootstrap.servers",
                parameterTool.get("kafka.bootstrap.servers", kafkaConfig.getBootstrapServers())));
        kafkaConfig.setAcks(parameterTool.get("alarm.notification.kafka.acks", kafkaConfig.getAcks()));
        kafkaConfig.setRetries(parameterTool.getInt("alarm.notification.kafka.retries", kafkaConfig.getRetries()));
        kafkaConfig.setBatchSize(parameterTool.getInt("alarm.notification.kafka.batch.size", kafkaConfig.getBatchSize()));
        kafkaConfig.setLingerMs(parameterTool.getInt("alarm.notification.kafka.linger.ms", kafkaConfig.getLingerMs()));
        
        // 监控配置
        config.setMonitoringEnabled(parameterTool.getBoolean("alarm.notification.monitoring.enabled", config.isMonitoringEnabled()));
        config.setMetricsInterval(parameterTool.getLong("alarm.notification.monitoring.metrics.interval", config.getMetricsInterval()));
        config.setPerformanceLogging(parameterTool.getBoolean("alarm.notification.monitoring.performance.logging", config.isPerformanceLogging()));
        config.setDetailedMetrics(parameterTool.getBoolean("alarm.notification.monitoring.detailed.metrics", config.isDetailedMetrics()));
        
        return config;
    }
    
    /**
     * 打印配置信息
     */
    public void printConfig() {
        log.info("=== 告警通知配置信息 ===");
        log.info("作业名称: {}", jobName);
        log.info("作业并行度: {}", jobParallelism);
        log.info("检查点间隔: {}ms", checkpointInterval);
        log.info("Kafka Bootstrap Servers: {}", kafkaBootstrapServers);
        log.info("输入主题: {}", inputTopic);
        log.info("订阅变更主题: {}", subscriptionChangesTopic);
        log.info("告警服务URL: {}", alarmServiceBaseUrl);
        log.info("通知并行度: {}", notificationParallelism);
        log.info("监控启用: {}", monitoringEnabled);
        log.info("========================");
    }
    
    /**
     * 邮件配置
     */
    @Data
    public static class EmailConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        /** SMTP服务器地址 */
        private String smtpHost = "localhost";

        /** SMTP服务器端口 */
        private int smtpPort = 587;

        /** 用户名 */
        private String username = "";

        /** 密码 */
        private String password = "";

        /** 发件人地址 */
        private String fromAddress = "<EMAIL>";

        /** 发件人名称 */
        private String fromName = "NTA安全系统";

        /** 是否启用TLS */
        private boolean tlsEnabled = true;

        /** 连接超时时间（毫秒） */
        private int connectionTimeout = 10000;

        /** 读取超时时间（毫秒） */
        private int readTimeout = 10000;
    }

    /**
     * Kafka配置
     */
    @Data
    public static class KafkaConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        /** Bootstrap服务器地址 */
        private String bootstrapServers = "localhost:9092";

        /** 生产者配置 */
        private String acks = "1";

        /** 重试次数 */
        private int retries = 3;

        /** 批量大小 */
        private int batchSize = 16384;

        /** 延迟时间（毫秒） */
        private int lingerMs = 1;

        /** 缓冲区内存大小 */
        private long bufferMemory = 33554432L;

        /** 请求超时时间（毫秒） */
        private int requestTimeoutMs = 30000;

        /** 连接超时时间（毫秒） */
        private int connectionTimeoutMs = 10000;
    }
}
