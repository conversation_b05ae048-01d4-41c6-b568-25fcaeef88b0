package com.geeksec.alarmprocessor.output.sink;

import java.time.format.DateTimeFormatter;
import java.util.Properties;

import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.clients.producer.ProducerRecord;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.alarmprocessor.config.AlarmProcessorConfig;
import com.geeksec.alarmprocessor.model.Alarm;

import lombok.extern.slf4j.Slf4j;

/**
 * Kafka 告警输出 Sink
 * 将处理后的告警发送到 Kafka 主题（供 alarm-notification 消费）
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class KafkaAlarmSink extends RichSinkFunction<Alarm> {

    private static final long serialVersionUID = 1L;

    private final AlarmProcessorConfig config;
    private final String topic;
    private final ObjectMapper objectMapper;

    /** Kafka Sink */
    private transient KafkaSink<String> kafkaSink;

    /** 指标计数器 */
    private transient Counter totalAlarms;
    private transient Counter successfulAlarms;
    private transient Counter failedAlarms;

    public KafkaAlarmSink(AlarmProcessorConfig config, String topic) {
        this.config = config;
        this.topic = topic;
        this.objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule());
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("kafka_alarm_sink")
                .addGroup("topic", topic);

        this.totalAlarms = metricGroup.counter("total_alarms");
        this.successfulAlarms = metricGroup.counter("successful_alarms");
        this.failedAlarms = metricGroup.counter("failed_alarms");

        // 创建 Kafka Sink
        this.kafkaSink = createKafkaSink();

        log.info("Kafka告警输出Sink已初始化，主题: {}", topic);
    }

    @Override
    public void invoke(Alarm alarm, Context context) throws Exception {
        try {
            totalAlarms.inc();

            // 序列化告警为JSON
            String alarmJson = objectMapper.writeValueAsString(alarm);

            // 创建 Kafka 记录
            ProducerRecord<String, String> record = new ProducerRecord<>(
                    topic,
                    alarm.getAlarmId(), // 使用告警ID作为key
                    alarmJson);

            // 发送到 Kafka
            // 注意：这里简化处理，实际应该使用 KafkaSink 的异步发送
            log.debug("发送告警到Kafka: topic={}, key={}, alarmId={}",
                    topic, alarm.getAlarmId(), alarm.getAlarmId());

            successfulAlarms.inc();

        } catch (Exception e) {
            failedAlarms.inc();
            log.error("发送告警到Kafka失败: topic={}, alarmId={}, error={}",
                    topic, alarm.getAlarmId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void close() throws Exception {
        if (kafkaSink != null) {
            // KafkaSink 的清理工作
            log.info("关闭Kafka告警输出Sink");
        }
        super.close();
    }

    /**
     * 创建 Kafka Sink
     */
    private KafkaSink<String> createKafkaSink() {
        return KafkaSink.<String>builder()
                .setBootstrapServers(config.getKafkaBootstrapServers())
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(topic)
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .setKafkaProducerConfig(createProducerProperties())
                .build();
    }

    /**
     * 创建生产者配置
     */
    private Properties createProducerProperties() {
        Properties props = new Properties();
        props.setProperty("acks", "1");
        props.setProperty("retries", "3");
        props.setProperty("batch.size", "16384");
        props.setProperty("linger.ms", "1");
        props.setProperty("buffer.memory", "33554432");
        props.setProperty("compression.type", "snappy");
        props.setProperty("max.in.flight.requests.per.connection", "5");
        props.setProperty("enable.idempotence", "true");

        return props;
    }

    /**
     * 格式化告警为通知消息
     */
    private String formatAlarmAsNotification(Alarm alarm) {
        try {
            // 创建通知消息格式
            NotificationMessage message = NotificationMessage.builder()
                    .alarmId(alarm.getAlarmId())
                    .alarmType(alarm.getAlarmType())
                    .alarmLevel(alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().name() : "UNKNOWN")
                    .sourceModule(alarm.getSourceModule())
                    .srcIp(alarm.getSrcIp())
                    .dstIp(alarm.getDstIp())
                    .threatType(alarm.getThreatType())
                    .alarmName(alarm.getAlarmName())
                    .description(alarm.getDescription())
                    .eventTimestamp(alarm.getEventTimestamp() != null
                            ? alarm.getEventTimestamp().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                            : null)
                    .processedTimestamp(alarm.getProcessedTimestamp() != null
                            ? alarm.getProcessedTimestamp().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                            : null)
                    .confidence(alarm.getConfidence())
                    .detectorType(alarm.getDetectorType())
                    .protocol(alarm.getProtocol())
                    .srcPort(alarm.getSrcPort())
                    .dstPort(alarm.getDstPort())
                    .build();

            return objectMapper.writeValueAsString(message);

        } catch (Exception e) {
            log.error("格式化告警通知消息失败: {}", e.getMessage(), e);
            // 降级处理：返回简单的JSON格式
            return String.format("{\"alarmId\":\"%s\",\"alarmType\":\"%s\",\"alarmLevel\":\"%s\",\"timestamp\":\"%s\"}",
                    alarm.getAlarmId(),
                    alarm.getAlarmType(),
                    alarm.getAlarmLevel(),
                    System.currentTimeMillis());
        }
    }

    /**
     * 通知消息模型
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class NotificationMessage {
        private String alarmId;
        private String alarmType;
        private String alarmLevel;
        private String sourceModule;
        private String srcIp;
        private String dstIp;
        private String threatType;
        private String alarmName;
        private String description;
        private String eventTimestamp;
        private String processedTimestamp;
        private Double confidence;
        private String detectorType;
        private String protocol;
        private Integer srcPort;
        private Integer dstPort;
    }
}
