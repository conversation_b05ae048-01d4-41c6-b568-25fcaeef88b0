package com.geeksec.nta.alarm.controller;

import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.dto.subscription.NotificationResultDto;
import com.geeksec.common.dto.subscription.NotificationSubscriptionDto;
import com.geeksec.nta.alarm.service.AlarmSubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 订阅配置同步控制器
 * 供 alarm-processor 调用的同步接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/alarm/subscription/sync")
@RequiredArgsConstructor
@Tag(name = "订阅配置同步", description = "供alarm-processor调用的同步接口")
public class SubscriptionSyncController extends BaseController {
    
    private final AlarmSubscriptionService subscriptionService;
    
    /**
     * 获取所有启用的订阅配置
     */
    @GetMapping("/active")
    @Operation(summary = "获取所有启用的订阅配置")
    public ApiResponse<List<NotificationSubscriptionDto>> getActiveSubscriptions() {
        log.debug("alarm-processor 请求获取所有启用的订阅配置");
        List<NotificationSubscriptionDto> subscriptions = subscriptionService.getActiveSubscriptions();
        log.info("返回启用的订阅配置数量: {}", subscriptions.size());
        return success(subscriptions);
    }
    
    /**
     * 获取指定时间后更新的订阅配置
     */
    @GetMapping("/updated")
    @Operation(summary = "获取更新的订阅配置")
    public ApiResponse<List<NotificationSubscriptionDto>> getUpdatedSubscriptions(@RequestParam String since) {
        log.debug("alarm-processor 请求获取更新的订阅配置，时间: {}", since);
        
        try {
            LocalDateTime sinceTime = LocalDateTime.parse(since, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            List<NotificationSubscriptionDto> subscriptions = subscriptionService.getUpdatedSubscriptions(sinceTime);
            log.info("返回更新的订阅配置数量: {}", subscriptions.size());
            return success(subscriptions);
        } catch (Exception e) {
            log.error("解析时间参数失败: {}", since, e);
            return error("时间参数格式错误");
        }
    }
    
    /**
     * 记录通知发送结果
     */
    @PostMapping("/notification-result")
    @Operation(summary = "记录通知发送结果")
    public ApiResponse<String> recordNotificationResult(@Valid @RequestBody List<NotificationResultDto> results) {
        log.debug("alarm-processor 上报通知发送结果，数量: {}", results.size());
        
        try {
            subscriptionService.recordNotificationResults(results);
            log.info("记录通知发送结果成功，数量: {}", results.size());
            return success("记录成功");
        } catch (Exception e) {
            log.error("记录通知发送结果失败", e);
            return error("记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查")
    public ApiResponse<String> health() {
        return success("OK");
    }
}
