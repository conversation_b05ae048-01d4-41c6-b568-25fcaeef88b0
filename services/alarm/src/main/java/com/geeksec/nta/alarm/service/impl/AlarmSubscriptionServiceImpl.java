package com.geeksec.nta.alarm.service.impl;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.common.exception.BusinessException;
import com.geeksec.common.exception.ErrorCode;
import com.geeksec.nta.alarm.dto.subscription.*;
import com.geeksec.common.dto.subscription.NotificationSubscriptionDto;
import com.geeksec.common.dto.subscription.NotificationResultDto;
import com.geeksec.nta.alarm.entity.AlarmSubscription;
import com.geeksec.nta.alarm.entity.NotificationLog;
import com.geeksec.nta.alarm.mapper.AlarmSubscriptionMapper;
import com.geeksec.nta.alarm.mapper.NotificationLogMapper;
import com.geeksec.nta.alarm.service.AlarmSubscriptionService;
import com.geeksec.nta.alarm.service.SubscriptionEventPublisher;
import com.mybatisflex.core.paginate.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.regex.Pattern;
import java.util.Map;

/**
 * 告警订阅服务实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmSubscriptionServiceImpl implements AlarmSubscriptionService {

    private final AlarmSubscriptionMapper subscriptionMapper;
    private final NotificationLogMapper notificationLogMapper;
    private final SubscriptionEventPublisher eventPublisher;
    
    @Override
    @Transactional
    public String createSubscription(CreateSubscriptionRequest request, String userId) {
        log.info("创建告警订阅，用户ID: {}, 订阅名称: {}", userId, request.getSubscriptionName());
        
        // 检查订阅名称是否重复
        long count = subscriptionMapper.countByUserIdAndName(userId, request.getSubscriptionName(), null);
        if (count > 0) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "订阅名称已存在");
        }
        
        // 创建订阅实体
        AlarmSubscription subscription = new AlarmSubscription();
        subscription.setId(UUID.randomUUID().toString().replace("-", ""));
        subscription.setUserId(userId);
        subscription.setSubscriptionName(request.getSubscriptionName());
        subscription.setDescription(request.getDescription());
        subscription.setEnabled(true);
        subscription.setPriorityLevel(request.getPriorityLevel());
        subscription.setMatchRules(request.getMatchRules());
        subscription.setNotificationChannels(request.getNotificationChannels());
        subscription.setFrequencyType(request.getFrequencyType());
        subscription.setFrequencyConfig(request.getFrequencyConfig());
        subscription.setQuietHoursEnabled(request.getQuietHoursEnabled());
        subscription.setQuietHoursConfig(request.getQuietHoursConfig());
        subscription.setTriggerCount(0);
        subscription.setCreatedBy(userId);
        subscription.setCreatedTime(LocalDateTime.now());
        subscription.setUpdatedBy(userId);
        subscription.setUpdatedTime(LocalDateTime.now());
        
        // 保存到数据库
        int result = subscriptionMapper.insert(subscription);
        if (result <= 0) {
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED, "创建订阅失败");
        }

        // 发布订阅创建事件
        eventPublisher.publishSubscriptionCreated(subscription, userId);

        log.info("创建告警订阅成功，订阅ID: {}", subscription.getId());
        return subscription.getId();
    }
    
    @Override
    @Transactional
    public boolean updateSubscription(String id, UpdateSubscriptionRequest request, String userId) {
        log.info("更新告警订阅，订阅ID: {}, 用户ID: {}", id, userId);
        
        // 查询现有订阅
        AlarmSubscription existing = subscriptionMapper.selectOneById(id);
        if (existing == null) {
            throw new BusinessException(ErrorCode.DATA_NOT_FOUND, "订阅不存在");
        }
        
        // 检查权限
        if (!userId.equals(existing.getUserId())) {
            throw new BusinessException(ErrorCode.ACCESS_DENIED, "无权限操作此订阅");
        }
        
        // 检查订阅名称是否重复
        long count = subscriptionMapper.countByUserIdAndName(userId, request.getSubscriptionName(), id);
        if (count > 0) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "订阅名称已存在");
        }
        
        // 更新订阅信息
        existing.setSubscriptionName(request.getSubscriptionName());
        existing.setDescription(request.getDescription());
        existing.setPriorityLevel(request.getPriorityLevel());
        existing.setMatchRules(request.getMatchRules());
        existing.setNotificationChannels(request.getNotificationChannels());
        existing.setFrequencyType(request.getFrequencyType());
        existing.setFrequencyConfig(request.getFrequencyConfig());
        existing.setQuietHoursEnabled(request.getQuietHoursEnabled());
        existing.setQuietHoursConfig(request.getQuietHoursConfig());
        existing.setUpdatedBy(userId);
        existing.setUpdatedTime(LocalDateTime.now());
        
        // 保存到数据库
        int result = subscriptionMapper.update(existing);
        if (result <= 0) {
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED, "更新订阅失败");
        }

        // 发布订阅更新事件
        eventPublisher.publishSubscriptionUpdated(existing, userId);

        log.info("更新告警订阅成功，订阅ID: {}", id);
        return true;
    }
    
    @Override
    @Transactional
    public boolean deleteSubscription(String id, String userId) {
        log.info("删除告警订阅，订阅ID: {}, 用户ID: {}", id, userId);
        
        // 查询现有订阅
        AlarmSubscription existing = subscriptionMapper.selectOneById(id);
        if (existing == null) {
            throw new BusinessException(ErrorCode.DATA_NOT_FOUND, "订阅不存在");
        }
        
        // 检查权限
        if (!userId.equals(existing.getUserId())) {
            throw new BusinessException(ErrorCode.ACCESS_DENIED, "无权限操作此订阅");
        }
        
        // 删除订阅
        int result = subscriptionMapper.deleteById(id);
        if (result <= 0) {
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED, "删除订阅失败");
        }

        // 发布订阅删除事件
        eventPublisher.publishSubscriptionDeleted(id, existing.getUserId(), userId);

        log.info("删除告警订阅成功，订阅ID: {}", id);
        return true;
    }
    
    @Override
    public AlarmSubscriptionVo getSubscription(String id, String userId) {
        log.debug("获取告警订阅详情，订阅ID: {}, 用户ID: {}", id, userId);
        
        AlarmSubscription subscription = subscriptionMapper.selectOneById(id);
        if (subscription == null) {
            throw new BusinessException(ErrorCode.DATA_NOT_FOUND, "订阅不存在");
        }
        
        // 检查权限
        if (!userId.equals(subscription.getUserId())) {
            throw new BusinessException(ErrorCode.ACCESS_DENIED, "无权限访问此订阅");
        }
        
        return convertToVo(subscription);
    }
    
    @Override
    public PageResultVo<AlarmSubscriptionVo> getSubscriptions(String userId, int page, int size, String keyword) {
        log.debug("分页查询告警订阅，用户ID: {}, 页码: {}, 页大小: {}, 关键词: {}", userId, page, size, keyword);
        
        Page<AlarmSubscription> pageParam = Page.of(page, size);
        Page<AlarmSubscription> result = subscriptionMapper.selectByUserIdWithPage(pageParam, userId, keyword);
        
        List<AlarmSubscriptionVo> voList = result.getRecords().stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());
        
        return new PageResultVo<>(voList, result.getTotalRow(), result.getPageNumber(), result.getPageSize());
    }
    
    @Override
    @Transactional
    public boolean toggleSubscription(String id, boolean enabled, String userId) {
        log.info("切换告警订阅状态，订阅ID: {}, 启用状态: {}, 用户ID: {}", id, enabled, userId);
        
        AlarmSubscription existing = subscriptionMapper.selectOneById(id);
        if (existing == null) {
            throw new BusinessException(ErrorCode.DATA_NOT_FOUND, "订阅不存在");
        }
        
        // 检查权限
        if (!userId.equals(existing.getUserId())) {
            throw new BusinessException(ErrorCode.ACCESS_DENIED, "无权限操作此订阅");
        }
        
        existing.setEnabled(enabled);
        existing.setUpdatedBy(userId);
        existing.setUpdatedTime(LocalDateTime.now());
        
        int result = subscriptionMapper.update(existing);
        if (result <= 0) {
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED, "更新订阅状态失败");
        }

        // 发布订阅状态变更事件
        eventPublisher.publishSubscriptionStatusChanged(existing, enabled, userId);

        log.info("切换告警订阅状态成功，订阅ID: {}", id);
        return true;
    }
    
    @Override
    public TestSubscriptionResult testSubscription(TestSubscriptionRequest request) {
        log.debug("测试订阅规则，规则数量: {}", request.getMatchRules().size());
        
        List<TestSubscriptionResult.RuleMatchDetail> details = request.getMatchRules().stream()
                .map(rule -> testSingleRule(rule, request.getTestData()))
                .collect(Collectors.toList());
        
        int matchedCount = (int) details.stream().mapToLong(detail -> detail.getMatched() ? 1 : 0).sum();
        int totalCount = details.size();
        
        return TestSubscriptionResult.success(matchedCount, totalCount, details);
    }
    
    /**
     * 测试单个规则
     */
    private TestSubscriptionResult.RuleMatchDetail testSingleRule(SubscriptionRuleDto rule, Map<String, Object> testData) {
        String fieldName = rule.getFieldName();
        Object actualValue = testData.get(fieldName);
        String actualValueStr = actualValue != null ? actualValue.toString() : "";
        
        boolean matched = false;
        String message = "";
        
        try {
            matched = matchRule(rule, actualValueStr);
            message = matched ? "匹配成功" : "匹配失败";
        } catch (Exception e) {
            message = "规则执行异常: " + e.getMessage();
        }
        
        return TestSubscriptionResult.RuleMatchDetail.builder()
                .ruleDescription(String.format("%s %s %s", fieldName, rule.getOperator(), rule.getExpectedValue()))
                .fieldName(fieldName)
                .operator(rule.getOperator().toString())
                .expectedValue(rule.getExpectedValue())
                .actualValue(actualValueStr)
                .matched(matched)
                .message(message)
                .build();
    }
    
    /**
     * 匹配单个规则
     */
    private boolean matchRule(SubscriptionRuleDto rule, String actualValue) {
        String expectedValue = rule.getExpectedValue();
        boolean ignoreCase = Boolean.TRUE.equals(rule.getIgnoreCase());
        
        if (ignoreCase) {
            actualValue = actualValue.toLowerCase();
            expectedValue = expectedValue.toLowerCase();
        }
        
        switch (rule.getOperator()) {
            case EQUALS:
                return actualValue.equals(expectedValue);
            case NOT_EQUALS:
                return !actualValue.equals(expectedValue);
            case CONTAINS:
                return actualValue.contains(expectedValue);
            case NOT_CONTAINS:
                return !actualValue.contains(expectedValue);
            case REGEX:
                return Pattern.matches(expectedValue, actualValue);
            case GREATER_THAN:
                return compareNumeric(actualValue, expectedValue) > 0;
            case LESS_THAN:
                return compareNumeric(actualValue, expectedValue) < 0;
            case GREATER_THAN_OR_EQUAL:
                return compareNumeric(actualValue, expectedValue) >= 0;
            case LESS_THAN_OR_EQUAL:
                return compareNumeric(actualValue, expectedValue) <= 0;
            case IN:
                String[] values = expectedValue.split(",");
                for (String value : values) {
                    if (actualValue.equals(value.trim())) {
                        return true;
                    }
                }
                return false;
            case NOT_IN:
                String[] notInValues = expectedValue.split(",");
                for (String value : notInValues) {
                    if (actualValue.equals(value.trim())) {
                        return false;
                    }
                }
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 数值比较
     */
    private int compareNumeric(String actual, String expected) {
        try {
            double actualNum = Double.parseDouble(actual);
            double expectedNum = Double.parseDouble(expected);
            return Double.compare(actualNum, expectedNum);
        } catch (NumberFormatException e) {
            // 如果不是数字，则按字符串比较
            return actual.compareTo(expected);
        }
    }
    
    @Override
    public List<NotificationSubscriptionDto> getActiveSubscriptions() {
        log.debug("获取所有启用的订阅配置");

        List<AlarmSubscription> subscriptions = subscriptionMapper.selectAllEnabled();
        return subscriptions.stream()
                .map(this::convertToNotificationDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationSubscriptionDto> getUpdatedSubscriptions(LocalDateTime since) {
        log.debug("获取指定时间后更新的订阅配置，时间: {}", since);

        List<AlarmSubscription> subscriptions = subscriptionMapper.selectUpdatedSince(since);
        return subscriptions.stream()
                .map(this::convertToNotificationDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void recordNotificationResults(List<NotificationResultDto> results) {
        log.debug("记录通知发送结果，数量: {}", results.size());

        if (results.isEmpty()) {
            return;
        }

        // 转换为通知日志实体
        List<NotificationLog> logs = results.stream()
                .map(this::convertToNotificationLog)
                .collect(Collectors.toList());

        // 批量插入通知记录
        notificationLogMapper.batchInsert(logs);

        // 更新订阅触发统计
        List<String> subscriptionIds = results.stream()
                .filter(result -> result.getSendStatus() == NotificationLog.SendStatus.SUCCESS)
                .map(NotificationResultDto::getSubscriptionId)
                .distinct()
                .collect(Collectors.toList());

        if (!subscriptionIds.isEmpty()) {
            subscriptionMapper.batchUpdateTriggerInfo(subscriptionIds, LocalDateTime.now());
        }

        log.info("记录通知发送结果完成，成功: {}, 失败: {}",
                results.stream().mapToLong(r -> r.getSendStatus() == NotificationLog.SendStatus.SUCCESS ? 1 : 0).sum(),
                results.stream().mapToLong(r -> r.getSendStatus() == NotificationLog.SendStatus.FAILED ? 1 : 0).sum());
    }

    /**
     * 转换为通知订阅 DTO
     */
    private NotificationSubscriptionDto convertToNotificationDto(AlarmSubscription subscription) {
        return NotificationSubscriptionDto.builder()
                .subscriptionId(subscription.getId())
                .userId(subscription.getUserId())
                .username(subscription.getUserId()) // 这里可以根据需要查询用户名
                .subscriptionName(subscription.getSubscriptionName())
                .enabled(subscription.getEnabled())
                .priorityLevel(subscription.getPriorityLevel())
                .rules(subscription.getMatchRules())
                .channels(subscription.getNotificationChannels())
                .frequencyType(subscription.getFrequencyType())
                .frequency(subscription.getFrequencyConfig())
                .quietHours(subscription.getQuietHoursConfig())
                .triggerCount(subscription.getTriggerCount())
                .lastTriggeredTime(subscription.getLastTriggeredTime())
                .createTime(subscription.getCreatedTime())
                .updateTime(subscription.getUpdatedTime())
                .build();
    }

    /**
     * 转换为通知日志实体
     */
    private NotificationLog convertToNotificationLog(NotificationResultDto result) {
        NotificationLog log = new NotificationLog();
        log.setId(UUID.randomUUID().toString().replace("-", ""));
        log.setSubscriptionId(result.getSubscriptionId());
        log.setAlarmId(result.getAlarmId());
        log.setChannelType(result.getChannelType());
        log.setRecipient(result.getRecipient());
        log.setSendStatus(result.getSendStatus());
        log.setSendTime(result.getSendTime());
        log.setErrorMessage(result.getErrorMessage());
        log.setRetryCount(result.getRetryCount());
        log.setSubject(result.getSubject());
        log.setContent(result.getContent());
        return log;
    }

    /**
     * 转换为 VO
     */
    private AlarmSubscriptionVo convertToVo(AlarmSubscription subscription) {
        AlarmSubscriptionVo vo = new AlarmSubscriptionVo();
        BeanUtils.copyProperties(subscription, vo);
        return vo;
    }
}
